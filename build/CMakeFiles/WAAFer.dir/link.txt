/usr/bin/c++  -arch arm64 -mmacosx-version-min=11.0 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/WAAFer.dir/WAAFer_autogen/mocs_compilation.cpp.o CMakeFiles/WAAFer.dir/src/main.cpp.o CMakeFiles/WAAFer.dir/src/core/AAFReader.cpp.o CMakeFiles/WAAFer.dir/src/core/LibAAFWrapper.cpp.o CMakeFiles/WAAFer.dir/src/core/MemoryManager.cpp.o CMakeFiles/WAAFer.dir/src/core/ChunkedFileReader.cpp.o CMakeFiles/WAAFer.dir/src/core/ProgressTracker.cpp.o CMakeFiles/WAAFer.dir/src/core/AnalysisSettings.cpp.o CMakeFiles/WAAFer.dir/src/core/TrackOrganizer.cpp.o CMakeFiles/WAAFer.dir/src/python/PythonBridge.cpp.o CMakeFiles/WAAFer.dir/src/python/PythonInterpreter.cpp.o CMakeFiles/WAAFer.dir/src/audio/AudioAnalyzer.cpp.o CMakeFiles/WAAFer.dir/src/audio/AudioFileManager.cpp.o CMakeFiles/WAAFer.dir/src/audio/ClassificationEngine.cpp.o CMakeFiles/WAAFer.dir/src/audio/AudioPlaybackManager.cpp.o CMakeFiles/WAAFer.dir/src/audio/WebRTCVAD.cpp.o CMakeFiles/WAAFer.dir/src/ai/LMStudioClient.cpp.o CMakeFiles/WAAFer.dir/src/export/AAFExporter.cpp.o CMakeFiles/WAAFer.dir/src/ui/MainWindow.cpp.o CMakeFiles/WAAFer.dir/src/ui/TimelineWidget.cpp.o CMakeFiles/WAAFer.dir/src/ui/ClassificationReviewDialog.cpp.o CMakeFiles/WAAFer.dir/src/ui/AnalysisSettingsDialog.cpp.o CMakeFiles/WAAFer.dir/src/utils/TimecodeUtils.cpp.o CMakeFiles/WAAFer.dir/src/ui/PresetManagementDialog.cpp.o CMakeFiles/WAAFer.dir/qrc_qml.cpp.o -o WAAFer.app/Contents/MacOS/WAAFer -F/opt/homebrew/lib  -Wl,-rpath,/opt/homebrew/lib /opt/homebrew/lib/QtWidgets.framework/Versions/A/QtWidgets /opt/homebrew/lib/QtQuick.framework/Versions/A/QtQuick /opt/homebrew/lib/QtMultimedia.framework/Versions/A/QtMultimedia /opt/homebrew/Frameworks/Python.framework/Versions/3.13/lib/libpython3.13.dylib lib/aaf -framework Foundation -framework CoreFoundation /opt/homebrew/lib/QtQmlMeta.framework/Versions/A/QtQmlMeta /opt/homebrew/lib/QtQmlWorkerScript.framework/Versions/A/QtQmlWorkerScript /opt/homebrew/lib/QtQmlModels.framework/Versions/A/QtQmlModels /opt/homebrew/lib/QtQml.framework/Versions/A/QtQml /opt/homebrew/lib/QtOpenGL.framework/Versions/A/QtOpenGL /opt/homebrew/lib/QtNetwork.framework/Versions/A/QtNetwork /opt/homebrew/lib/QtGui.framework/Versions/A/QtGui -framework AGL -framework AppKit -framework OpenGL -framework ImageIO -framework Metal /opt/homebrew/lib/QtCore.framework/Versions/A/QtCore -framework IOKit -framework DiskArbitration -framework UniformTypeIdentifiers
