# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# compile CX<PERSON> with /usr/bin/c++
CXX_DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB

CXX_INCLUDES = -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers

CXX_FLAGSarm64 = -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0

CXX_FLAGS = -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0

