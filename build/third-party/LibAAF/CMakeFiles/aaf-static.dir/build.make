# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Volumes/Projects/_Code/_Waafer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Volumes/Projects/_Code/_Waafer/build

# Include any dependencies generated for this target.
include third-party/LibAAF/CMakeFiles/aaf-static.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include third-party/LibAAF/CMakeFiles/aaf-static.dir/compiler_depend.make

# Include the progress variables for this target.
include third-party/LibAAF/CMakeFiles/aaf-static.dir/progress.make

# Include the compile flags for this target's objects.
include third-party/LibAAF/CMakeFiles/aaf-static.dir/flags.make

third-party/LibAAF/aaf-static_autogen/timestamp: /opt/homebrew/share/qt/libexec/moc
third-party/LibAAF/aaf-static_autogen/timestamp: /opt/homebrew/share/qt/libexec/uic
third-party/LibAAF/aaf-static_autogen/timestamp: third-party/LibAAF/CMakeFiles/aaf-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC and UIC for target aaf-static"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/local/bin/cmake -E cmake_autogen /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/AutogenInfo.json ""
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/local/bin/cmake -E touch /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/timestamp

third-party/LibAAF/CMakeFiles/aaf-static.dir/codegen:
.PHONY : third-party/LibAAF/CMakeFiles/aaf-static.dir/codegen

third-party/LibAAF/CMakeFiles/aaf-static.dir/aaf-static_autogen/mocs_compilation.cpp.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/flags.make
third-party/LibAAF/CMakeFiles/aaf-static.dir/aaf-static_autogen/mocs_compilation.cpp.o: third-party/LibAAF/aaf-static_autogen/mocs_compilation.cpp
third-party/LibAAF/CMakeFiles/aaf-static.dir/aaf-static_autogen/mocs_compilation.cpp.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object third-party/LibAAF/CMakeFiles/aaf-static.dir/aaf-static_autogen/mocs_compilation.cpp.o"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT third-party/LibAAF/CMakeFiles/aaf-static.dir/aaf-static_autogen/mocs_compilation.cpp.o -MF CMakeFiles/aaf-static.dir/aaf-static_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/aaf-static.dir/aaf-static_autogen/mocs_compilation.cpp.o -c /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/mocs_compilation.cpp

third-party/LibAAF/CMakeFiles/aaf-static.dir/aaf-static_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/aaf-static.dir/aaf-static_autogen/mocs_compilation.cpp.i"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/mocs_compilation.cpp > CMakeFiles/aaf-static.dir/aaf-static_autogen/mocs_compilation.cpp.i

third-party/LibAAF/CMakeFiles/aaf-static.dir/aaf-static_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/aaf-static.dir/aaf-static_autogen/mocs_compilation.cpp.s"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/mocs_compilation.cpp -o CMakeFiles/aaf-static.dir/aaf-static_autogen/mocs_compilation.cpp.s

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/LibCFB/LibCFB.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/flags.make
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/LibCFB/LibCFB.c.o: /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/LibCFB/LibCFB.c
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/LibCFB/LibCFB.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object third-party/LibAAF/CMakeFiles/aaf-static.dir/src/LibCFB/LibCFB.c.o"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/LibAAF/CMakeFiles/aaf-static.dir/src/LibCFB/LibCFB.c.o -MF CMakeFiles/aaf-static.dir/src/LibCFB/LibCFB.c.o.d -o CMakeFiles/aaf-static.dir/src/LibCFB/LibCFB.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/LibCFB/LibCFB.c

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/LibCFB/LibCFB.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/aaf-static.dir/src/LibCFB/LibCFB.c.i"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/LibCFB/LibCFB.c > CMakeFiles/aaf-static.dir/src/LibCFB/LibCFB.c.i

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/LibCFB/LibCFB.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/aaf-static.dir/src/LibCFB/LibCFB.c.s"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/LibCFB/LibCFB.c -o CMakeFiles/aaf-static.dir/src/LibCFB/LibCFB.c.s

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/LibCFB/CFBDump.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/flags.make
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/LibCFB/CFBDump.c.o: /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/LibCFB/CFBDump.c
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/LibCFB/CFBDump.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object third-party/LibAAF/CMakeFiles/aaf-static.dir/src/LibCFB/CFBDump.c.o"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/LibAAF/CMakeFiles/aaf-static.dir/src/LibCFB/CFBDump.c.o -MF CMakeFiles/aaf-static.dir/src/LibCFB/CFBDump.c.o.d -o CMakeFiles/aaf-static.dir/src/LibCFB/CFBDump.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/LibCFB/CFBDump.c

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/LibCFB/CFBDump.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/aaf-static.dir/src/LibCFB/CFBDump.c.i"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/LibCFB/CFBDump.c > CMakeFiles/aaf-static.dir/src/LibCFB/CFBDump.c.i

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/LibCFB/CFBDump.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/aaf-static.dir/src/LibCFB/CFBDump.c.s"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/LibCFB/CFBDump.c -o CMakeFiles/aaf-static.dir/src/LibCFB/CFBDump.c.s

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFCore.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/flags.make
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFCore.c.o: /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFCore.c
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFCore.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFCore.c.o"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFCore.c.o -MF CMakeFiles/aaf-static.dir/src/AAFCore/AAFCore.c.o.d -o CMakeFiles/aaf-static.dir/src/AAFCore/AAFCore.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFCore.c

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFCore.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/aaf-static.dir/src/AAFCore/AAFCore.c.i"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFCore.c > CMakeFiles/aaf-static.dir/src/AAFCore/AAFCore.c.i

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFCore.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/aaf-static.dir/src/AAFCore/AAFCore.c.s"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFCore.c -o CMakeFiles/aaf-static.dir/src/AAFCore/AAFCore.c.s

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFClass.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/flags.make
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFClass.c.o: /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFClass.c
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFClass.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFClass.c.o"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFClass.c.o -MF CMakeFiles/aaf-static.dir/src/AAFCore/AAFClass.c.o.d -o CMakeFiles/aaf-static.dir/src/AAFCore/AAFClass.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFClass.c

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFClass.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/aaf-static.dir/src/AAFCore/AAFClass.c.i"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFClass.c > CMakeFiles/aaf-static.dir/src/AAFCore/AAFClass.c.i

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFClass.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/aaf-static.dir/src/AAFCore/AAFClass.c.s"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFClass.c -o CMakeFiles/aaf-static.dir/src/AAFCore/AAFClass.c.s

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFToText.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/flags.make
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFToText.c.o: /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFToText.c
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFToText.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFToText.c.o"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFToText.c.o -MF CMakeFiles/aaf-static.dir/src/AAFCore/AAFToText.c.o.d -o CMakeFiles/aaf-static.dir/src/AAFCore/AAFToText.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFToText.c

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFToText.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/aaf-static.dir/src/AAFCore/AAFToText.c.i"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFToText.c > CMakeFiles/aaf-static.dir/src/AAFCore/AAFToText.c.i

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFToText.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/aaf-static.dir/src/AAFCore/AAFToText.c.s"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFToText.c -o CMakeFiles/aaf-static.dir/src/AAFCore/AAFToText.c.s

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFDump.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/flags.make
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFDump.c.o: /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFDump.c
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFDump.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFDump.c.o"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFDump.c.o -MF CMakeFiles/aaf-static.dir/src/AAFCore/AAFDump.c.o.d -o CMakeFiles/aaf-static.dir/src/AAFCore/AAFDump.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFDump.c

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFDump.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/aaf-static.dir/src/AAFCore/AAFDump.c.i"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFDump.c > CMakeFiles/aaf-static.dir/src/AAFCore/AAFDump.c.i

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFDump.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/aaf-static.dir/src/AAFCore/AAFDump.c.s"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFDump.c -o CMakeFiles/aaf-static.dir/src/AAFCore/AAFDump.c.s

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIface.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/flags.make
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIface.c.o: /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/AAFIface.c
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIface.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIface.c.o"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIface.c.o -MF CMakeFiles/aaf-static.dir/src/AAFIface/AAFIface.c.o.d -o CMakeFiles/aaf-static.dir/src/AAFIface/AAFIface.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/AAFIface.c

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIface.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/aaf-static.dir/src/AAFIface/AAFIface.c.i"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/AAFIface.c > CMakeFiles/aaf-static.dir/src/AAFIface/AAFIface.c.i

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIface.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/aaf-static.dir/src/AAFIface/AAFIface.c.s"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/AAFIface.c -o CMakeFiles/aaf-static.dir/src/AAFIface/AAFIface.c.s

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIParser.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/flags.make
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIParser.c.o: /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/AAFIParser.c
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIParser.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIParser.c.o"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIParser.c.o -MF CMakeFiles/aaf-static.dir/src/AAFIface/AAFIParser.c.o.d -o CMakeFiles/aaf-static.dir/src/AAFIface/AAFIParser.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/AAFIParser.c

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIParser.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/aaf-static.dir/src/AAFIface/AAFIParser.c.i"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/AAFIParser.c > CMakeFiles/aaf-static.dir/src/AAFIface/AAFIParser.c.i

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIParser.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/aaf-static.dir/src/AAFIface/AAFIParser.c.s"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/AAFIParser.c -o CMakeFiles/aaf-static.dir/src/AAFIface/AAFIParser.c.s

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIEssenceFile.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/flags.make
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIEssenceFile.c.o: /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/AAFIEssenceFile.c
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIEssenceFile.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIEssenceFile.c.o"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIEssenceFile.c.o -MF CMakeFiles/aaf-static.dir/src/AAFIface/AAFIEssenceFile.c.o.d -o CMakeFiles/aaf-static.dir/src/AAFIface/AAFIEssenceFile.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/AAFIEssenceFile.c

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIEssenceFile.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/aaf-static.dir/src/AAFIface/AAFIEssenceFile.c.i"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/AAFIEssenceFile.c > CMakeFiles/aaf-static.dir/src/AAFIface/AAFIEssenceFile.c.i

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIEssenceFile.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/aaf-static.dir/src/AAFIface/AAFIEssenceFile.c.s"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/AAFIEssenceFile.c -o CMakeFiles/aaf-static.dir/src/AAFIface/AAFIEssenceFile.c.s

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/RIFFParser.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/flags.make
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/RIFFParser.c.o: /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/RIFFParser.c
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/RIFFParser.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/RIFFParser.c.o"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/RIFFParser.c.o -MF CMakeFiles/aaf-static.dir/src/AAFIface/RIFFParser.c.o.d -o CMakeFiles/aaf-static.dir/src/AAFIface/RIFFParser.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/RIFFParser.c

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/RIFFParser.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/aaf-static.dir/src/AAFIface/RIFFParser.c.i"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/RIFFParser.c > CMakeFiles/aaf-static.dir/src/AAFIface/RIFFParser.c.i

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/RIFFParser.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/aaf-static.dir/src/AAFIface/RIFFParser.c.s"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/RIFFParser.c -o CMakeFiles/aaf-static.dir/src/AAFIface/RIFFParser.c.s

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/URIParser.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/flags.make
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/URIParser.c.o: /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/URIParser.c
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/URIParser.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/URIParser.c.o"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/URIParser.c.o -MF CMakeFiles/aaf-static.dir/src/AAFIface/URIParser.c.o.d -o CMakeFiles/aaf-static.dir/src/AAFIface/URIParser.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/URIParser.c

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/URIParser.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/aaf-static.dir/src/AAFIface/URIParser.c.i"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/URIParser.c > CMakeFiles/aaf-static.dir/src/AAFIface/URIParser.c.i

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/URIParser.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/aaf-static.dir/src/AAFIface/URIParser.c.s"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/URIParser.c -o CMakeFiles/aaf-static.dir/src/AAFIface/URIParser.c.s

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/ProTools.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/flags.make
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/ProTools.c.o: /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/ProTools.c
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/ProTools.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/ProTools.c.o"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/ProTools.c.o -MF CMakeFiles/aaf-static.dir/src/AAFIface/ProTools.c.o.d -o CMakeFiles/aaf-static.dir/src/AAFIface/ProTools.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/ProTools.c

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/ProTools.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/aaf-static.dir/src/AAFIface/ProTools.c.i"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/ProTools.c > CMakeFiles/aaf-static.dir/src/AAFIface/ProTools.c.i

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/ProTools.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/aaf-static.dir/src/AAFIface/ProTools.c.s"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/ProTools.c -o CMakeFiles/aaf-static.dir/src/AAFIface/ProTools.c.s

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/Resolve.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/flags.make
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/Resolve.c.o: /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/Resolve.c
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/Resolve.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/Resolve.c.o"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/Resolve.c.o -MF CMakeFiles/aaf-static.dir/src/AAFIface/Resolve.c.o.d -o CMakeFiles/aaf-static.dir/src/AAFIface/Resolve.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/Resolve.c

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/Resolve.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/aaf-static.dir/src/AAFIface/Resolve.c.i"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/Resolve.c > CMakeFiles/aaf-static.dir/src/AAFIface/Resolve.c.i

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/Resolve.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/aaf-static.dir/src/AAFIface/Resolve.c.s"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/Resolve.c -o CMakeFiles/aaf-static.dir/src/AAFIface/Resolve.c.s

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/MediaComposer.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/flags.make
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/MediaComposer.c.o: /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/MediaComposer.c
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/MediaComposer.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/MediaComposer.c.o"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/MediaComposer.c.o -MF CMakeFiles/aaf-static.dir/src/AAFIface/MediaComposer.c.o.d -o CMakeFiles/aaf-static.dir/src/AAFIface/MediaComposer.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/MediaComposer.c

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/MediaComposer.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/aaf-static.dir/src/AAFIface/MediaComposer.c.i"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/MediaComposer.c > CMakeFiles/aaf-static.dir/src/AAFIface/MediaComposer.c.i

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/MediaComposer.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/aaf-static.dir/src/AAFIface/MediaComposer.c.s"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/MediaComposer.c -o CMakeFiles/aaf-static.dir/src/AAFIface/MediaComposer.c.s

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/common/utils.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/flags.make
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/common/utils.c.o: /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/common/utils.c
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/common/utils.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object third-party/LibAAF/CMakeFiles/aaf-static.dir/src/common/utils.c.o"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/LibAAF/CMakeFiles/aaf-static.dir/src/common/utils.c.o -MF CMakeFiles/aaf-static.dir/src/common/utils.c.o.d -o CMakeFiles/aaf-static.dir/src/common/utils.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/common/utils.c

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/common/utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/aaf-static.dir/src/common/utils.c.i"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/common/utils.c > CMakeFiles/aaf-static.dir/src/common/utils.c.i

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/common/utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/aaf-static.dir/src/common/utils.c.s"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/common/utils.c -o CMakeFiles/aaf-static.dir/src/common/utils.c.s

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/common/log.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/flags.make
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/common/log.c.o: /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/common/log.c
third-party/LibAAF/CMakeFiles/aaf-static.dir/src/common/log.c.o: third-party/LibAAF/CMakeFiles/aaf-static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object third-party/LibAAF/CMakeFiles/aaf-static.dir/src/common/log.c.o"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT third-party/LibAAF/CMakeFiles/aaf-static.dir/src/common/log.c.o -MF CMakeFiles/aaf-static.dir/src/common/log.c.o.d -o CMakeFiles/aaf-static.dir/src/common/log.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/common/log.c

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/common/log.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/aaf-static.dir/src/common/log.c.i"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/common/log.c > CMakeFiles/aaf-static.dir/src/common/log.c.i

third-party/LibAAF/CMakeFiles/aaf-static.dir/src/common/log.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/aaf-static.dir/src/common/log.c.s"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/common/log.c -o CMakeFiles/aaf-static.dir/src/common/log.c.s

# Object files for target aaf-static
aaf__static_OBJECTS = \
"CMakeFiles/aaf-static.dir/aaf-static_autogen/mocs_compilation.cpp.o" \
"CMakeFiles/aaf-static.dir/src/LibCFB/LibCFB.c.o" \
"CMakeFiles/aaf-static.dir/src/LibCFB/CFBDump.c.o" \
"CMakeFiles/aaf-static.dir/src/AAFCore/AAFCore.c.o" \
"CMakeFiles/aaf-static.dir/src/AAFCore/AAFClass.c.o" \
"CMakeFiles/aaf-static.dir/src/AAFCore/AAFToText.c.o" \
"CMakeFiles/aaf-static.dir/src/AAFCore/AAFDump.c.o" \
"CMakeFiles/aaf-static.dir/src/AAFIface/AAFIface.c.o" \
"CMakeFiles/aaf-static.dir/src/AAFIface/AAFIParser.c.o" \
"CMakeFiles/aaf-static.dir/src/AAFIface/AAFIEssenceFile.c.o" \
"CMakeFiles/aaf-static.dir/src/AAFIface/RIFFParser.c.o" \
"CMakeFiles/aaf-static.dir/src/AAFIface/URIParser.c.o" \
"CMakeFiles/aaf-static.dir/src/AAFIface/ProTools.c.o" \
"CMakeFiles/aaf-static.dir/src/AAFIface/Resolve.c.o" \
"CMakeFiles/aaf-static.dir/src/AAFIface/MediaComposer.c.o" \
"CMakeFiles/aaf-static.dir/src/common/utils.c.o" \
"CMakeFiles/aaf-static.dir/src/common/log.c.o"

# External object files for target aaf-static
aaf__static_EXTERNAL_OBJECTS =

lib/aaf: third-party/LibAAF/CMakeFiles/aaf-static.dir/aaf-static_autogen/mocs_compilation.cpp.o
lib/aaf: third-party/LibAAF/CMakeFiles/aaf-static.dir/src/LibCFB/LibCFB.c.o
lib/aaf: third-party/LibAAF/CMakeFiles/aaf-static.dir/src/LibCFB/CFBDump.c.o
lib/aaf: third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFCore.c.o
lib/aaf: third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFClass.c.o
lib/aaf: third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFToText.c.o
lib/aaf: third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFDump.c.o
lib/aaf: third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIface.c.o
lib/aaf: third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIParser.c.o
lib/aaf: third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIEssenceFile.c.o
lib/aaf: third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/RIFFParser.c.o
lib/aaf: third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/URIParser.c.o
lib/aaf: third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/ProTools.c.o
lib/aaf: third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/Resolve.c.o
lib/aaf: third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/MediaComposer.c.o
lib/aaf: third-party/LibAAF/CMakeFiles/aaf-static.dir/src/common/utils.c.o
lib/aaf: third-party/LibAAF/CMakeFiles/aaf-static.dir/src/common/log.c.o
lib/aaf: third-party/LibAAF/CMakeFiles/aaf-static.dir/build.make
lib/aaf: third-party/LibAAF/CMakeFiles/aaf-static.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Linking CXX static library ../../lib/aaf"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && $(CMAKE_COMMAND) -P CMakeFiles/aaf-static.dir/cmake_clean_target.cmake
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/aaf-static.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
third-party/LibAAF/CMakeFiles/aaf-static.dir/build: lib/aaf
.PHONY : third-party/LibAAF/CMakeFiles/aaf-static.dir/build

third-party/LibAAF/CMakeFiles/aaf-static.dir/clean:
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && $(CMAKE_COMMAND) -P CMakeFiles/aaf-static.dir/cmake_clean.cmake
.PHONY : third-party/LibAAF/CMakeFiles/aaf-static.dir/clean

third-party/LibAAF/CMakeFiles/aaf-static.dir/depend: third-party/LibAAF/aaf-static_autogen/timestamp
	cd /Volumes/Projects/_Code/_Waafer/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Volumes/Projects/_Code/_Waafer /Volumes/Projects/_Code/_Waafer/third-party/LibAAF /Volumes/Projects/_Code/_Waafer/build /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/CMakeFiles/aaf-static.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : third-party/LibAAF/CMakeFiles/aaf-static.dir/depend

