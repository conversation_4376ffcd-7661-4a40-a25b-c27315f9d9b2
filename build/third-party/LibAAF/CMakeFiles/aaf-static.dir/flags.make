# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# compile C with /usr/bin/cc
# compile CXX with /usr/bin/c++
C_DEFINES = 

C_INCLUDES = -I/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/Volumes/Projects/_Code/_Waafer/build/include

C_FLAGSarm64 = -std=gnu99 -arch arm64 -mmacosx-version-min=11.0

C_FLAGS = -std=gnu99 -arch arm64 -mmacosx-version-min=11.0

CXX_DEFINES = 

CXX_INCLUDES = -I/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/Volumes/Projects/_Code/_Waafer/build/include

CXX_FLAGSarm64 = -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0

CXX_FLAGS = -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0

