# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Volumes/Projects/_Code/_Waafer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Volumes/Projects/_Code/_Waafer/build

# Utility rule file for aaf-static_autogen.

# Include any custom commands dependencies for this target.
include third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/compiler_depend.make

# Include the progress variables for this target.
include third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/progress.make

third-party/LibAAF/CMakeFiles/aaf-static_autogen: third-party/LibAAF/aaf-static_autogen/timestamp

third-party/LibAAF/aaf-static_autogen/timestamp: /opt/homebrew/share/qt/libexec/moc
third-party/LibAAF/aaf-static_autogen/timestamp: /opt/homebrew/share/qt/libexec/uic
third-party/LibAAF/aaf-static_autogen/timestamp: third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Volumes/Projects/_Code/_Waafer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC and UIC for target aaf-static"
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/local/bin/cmake -E cmake_autogen /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/AutogenInfo.json ""
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && /usr/local/bin/cmake -E touch /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/timestamp

third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/codegen:
.PHONY : third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/codegen

aaf-static_autogen: third-party/LibAAF/CMakeFiles/aaf-static_autogen
aaf-static_autogen: third-party/LibAAF/aaf-static_autogen/timestamp
aaf-static_autogen: third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/build.make
.PHONY : aaf-static_autogen

# Rule to build all files generated by this target.
third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/build: aaf-static_autogen
.PHONY : third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/build

third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/clean:
	cd /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF && $(CMAKE_COMMAND) -P CMakeFiles/aaf-static_autogen.dir/cmake_clean.cmake
.PHONY : third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/clean

third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/depend:
	cd /Volumes/Projects/_Code/_Waafer/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Volumes/Projects/_Code/_Waafer /Volumes/Projects/_Code/_Waafer/third-party/LibAAF /Volumes/Projects/_Code/_Waafer/build /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : third-party/LibAAF/CMakeFiles/aaf-static_autogen.dir/depend

