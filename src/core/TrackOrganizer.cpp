#include "TrackOrganizer.h"
#include "../ai/LMStudioClient.h"
#include <QSettings>
#include <QStandardPaths>
#include <QDir>
#include <QJsonDocument>
#include <QJsonObject>

TrackOrganizer::TrackOrganizer(QObject *parent)
    : QObject(parent)
    , m_currentTemplate("Standard")
    , m_preserveOriginalTiming(true)
    , m_lmStudioClient(nullptr)
    , m_performanceOptimizations(true)
{
    initializeDefaultTemplates();
    loadTemplates();

    qDebug() << "TrackOrganizer initialized with" << m_templates.size() << "templates";
}

TrackOrganizer::~TrackOrganizer()
{
    saveTemplates();
}

void TrackOrganizer::setCurrentTemplate(const QString &templateName)
{
    if (m_currentTemplate != templateName && m_templates.contains(templateName)) {
        m_currentTemplate = templateName;
        emit currentTemplateChanged();
        qDebug() << "Current template set to:" << templateName;
    }
}

void TrackOrganizer::setPreserveOriginalTiming(bool preserve)
{
    if (m_preserveOriginalTiming != preserve) {
        m_preserveOriginalTiming = preserve;
        emit preserveOriginalTimingChanged();
        qDebug() << "Preserve original timing:" << preserve;
    }
}

void TrackOrganizer::setLMStudioClient(LMStudioClient *client)
{
    m_lmStudioClient = client;
    qDebug() << "LMStudioClient set for TrackOrganizer:" << (client ? "Available" : "Not available");
}

void TrackOrganizer::setSpeakerDatabase(const QVariantMap &speakerStats)
{
    m_speakerDatabase = speakerStats;
    qDebug() << "Speaker database updated with" << speakerStats.value("totalSpeakers", 0).toInt() << "speakers";
}

void TrackOrganizer::setPerformanceOptimizations(bool enabled)
{
    m_performanceOptimizations = enabled;
    if (enabled) {
        qDebug() << "Performance optimizations enabled";
    } else {
        qDebug() << "Performance optimizations disabled";
        m_organizationCache.clear(); // Clear cache when disabled
    }
}

QVariantList TrackOrganizer::organizeRegions(const QVariantList &regions, const QVariantList &originalTracks)
{
    if (!m_templates.contains(m_currentTemplate)) {
        qWarning() << "Template not found:" << m_currentTemplate;
        return QVariantList();
    }

    const TrackTemplate &trackTemplate = m_templates[m_currentTemplate];
    QList<TrackAssignment> assignments;

    qDebug() << "Organizing" << regions.size() << "regions using template:" << m_currentTemplate;
    qDebug() << "Template settings - separateSpeakers:" << trackTemplate.separateSpeakers
             << "groupSimilarContent:" << trackTemplate.groupSimilarContent
             << "maxTracksPerType:" << trackTemplate.maxTracksPerType;

    // Use LM Studio for AI-assisted organization if available
    if (m_lmStudioClient && m_lmStudioClient->serverStatus() == LMStudioClient::Available) {
        qDebug() << "Using LM Studio for AI-assisted track organization";

        // Generate organization suggestions using LM Studio
        m_lmStudioClient->generateOrganizationSuggestions(regions, m_currentTemplate);
        qDebug() << "LM Studio organization request sent";
    } else {
        qDebug() << "LM Studio not available, using rule-based organization";
    }

    // Process each region with template-specific logic
    for (const QVariant &regionVariant : regions) {
        QVariantMap region = regionVariant.toMap();

        // Always use template-specific assignment logic to ensure differentiation
        TrackAssignment assignment = assignRegionToTrackWithTemplate(region, trackTemplate, assignments);

        assignments.append(assignment);
    }
    
    // Resolve conflicts
    QVariantList resolvedAssignments;
    for (const auto &assignment : assignments) {
        resolvedAssignments.append(assignmentToVariantMap(assignment));
    }
    
    resolvedAssignments = resolveConflicts(resolvedAssignments);
    
    // Generate statistics
    QVariantMap stats = getOrganizationStats(resolvedAssignments);
    
    emit organizationCompleted(resolvedAssignments, stats);
    
    qDebug() << "Organization completed:" << resolvedAssignments.size() << "assignments";
    
    return resolvedAssignments;
}

void TrackOrganizer::createCustomTemplate(const QString &templateName, const QVariantMap &templateConfig)
{
    TrackTemplate template_;
    template_.name = templateName;
    template_.description = templateConfig.value("description", "Custom template").toString();
    template_.trackRules = templateConfig.value("trackRules").toMap();
    template_.speakerRules = templateConfig.value("speakerRules").toMap();
    template_.conflictRules = templateConfig.value("conflictRules").toMap();
    template_.separateSpeakers = templateConfig.value("separateSpeakers", false).toBool();
    template_.groupSimilarContent = templateConfig.value("groupSimilarContent", true).toBool();
    template_.maxTracksPerType = templateConfig.value("maxTracksPerType", 5).toInt();
    
    m_templates[templateName] = template_;
    
    if (!m_templateNames.contains(templateName)) {
        m_templateNames.append(templateName);
        emit availableTemplatesChanged();
    }
    
    qDebug() << "Created custom template:" << templateName;
}

void TrackOrganizer::deleteTemplate(const QString &templateName)
{
    if (m_templates.contains(templateName) && templateName != "Standard") {
        m_templates.remove(templateName);
        m_templateNames.removeAll(templateName);
        
        if (m_currentTemplate == templateName) {
            setCurrentTemplate("Standard");
        }
        
        emit availableTemplatesChanged();
        qDebug() << "Deleted template:" << templateName;
    }
}

QVariantMap TrackOrganizer::getTemplateConfig(const QString &templateName) const
{
    if (!m_templates.contains(templateName)) {
        return QVariantMap();
    }
    
    const TrackTemplate &template_ = m_templates[templateName];
    QVariantMap config;
    
    config["name"] = template_.name;
    config["description"] = template_.description;
    config["trackRules"] = template_.trackRules;
    config["speakerRules"] = template_.speakerRules;
    config["conflictRules"] = template_.conflictRules;
    config["separateSpeakers"] = template_.separateSpeakers;
    config["groupSimilarContent"] = template_.groupSimilarContent;
    config["maxTracksPerType"] = template_.maxTracksPerType;
    
    return config;
}

QVariantList TrackOrganizer::previewOrganization(const QVariantList &regions, const QString &templateName) const
{
    if (!m_templates.contains(templateName)) {
        return QVariantList();
    }
    
    const TrackTemplate &trackTemplate = m_templates[templateName];
    QList<TrackAssignment> assignments;

    // Process each region for preview using template-specific logic
    for (const QVariant &regionVariant : regions) {
        QVariantMap region = regionVariant.toMap();
        TrackAssignment assignment = assignRegionToTrackWithTemplate(region, trackTemplate, assignments);
        assignments.append(assignment);
    }
    
    QVariantList preview;
    for (const auto &assignment : assignments) {
        preview.append(assignmentToVariantMap(assignment));
    }
    
    return preview;
}

QVariantList TrackOrganizer::resolveConflicts(const QVariantList &assignments)
{
    QList<TrackAssignment> assignmentList;
    
    // Convert to internal format
    for (const QVariant &assignmentVariant : assignments) {
        assignmentList.append(variantMapToAssignment(assignmentVariant.toMap()));
    }
    
    // Resolve conflicts with enhanced logic
    for (int i = 0; i < assignmentList.size(); ++i) {
        if (assignmentList[i].hasConflict) {
            if (m_performanceOptimizations && !m_speakerDatabase.isEmpty()) {
                assignmentList[i] = resolveConflictSmart(assignmentList[i], assignmentList);
            } else {
                assignmentList[i] = resolveConflict(assignmentList[i], assignmentList);
            }
        }
    }
    
    // Convert back to variant list
    QVariantList resolved;
    for (const auto &assignment : assignmentList) {
        resolved.append(assignmentToVariantMap(assignment));
    }
    
    return resolved;
}

QVariantMap TrackOrganizer::getOrganizationStats(const QVariantList &assignments) const
{
    QVariantMap stats;
    QMap<QString, int> trackCounts;
    QMap<QString, int> contentTypeCounts;
    int conflictCount = 0;
    
    for (const QVariant &assignmentVariant : assignments) {
        QVariantMap assignment = assignmentVariant.toMap();
        QString trackName = assignment.value("assignedTrack").toString();
        QString contentType = assignment.value("contentType").toString();
        bool hasConflict = assignment.value("hasConflict").toBool();
        
        trackCounts[trackName]++;
        contentTypeCounts[contentType]++;
        
        if (hasConflict) {
            conflictCount++;
        }
    }
    
    stats["totalRegions"] = assignments.size();
    stats["totalTracks"] = trackCounts.size();
    stats["conflictCount"] = conflictCount;
    // Convert QMap<QString, int> to QVariantMap
    QVariantMap trackCountsVariant;
    for (auto it = trackCounts.begin(); it != trackCounts.end(); ++it) {
        trackCountsVariant[it.key()] = it.value();
    }

    QVariantMap contentTypeCountsVariant;
    for (auto it = contentTypeCounts.begin(); it != contentTypeCounts.end(); ++it) {
        contentTypeCountsVariant[it.key()] = it.value();
    }

    stats["trackCounts"] = trackCountsVariant;
    stats["contentTypeCounts"] = contentTypeCountsVariant;
    
    return stats;
}

void TrackOrganizer::initializeDefaultTemplates()
{
    // Standard template
    m_templates["Standard"] = createStandardTemplate();
    m_templateNames.append("Standard");
    
    // Speaker-based template
    m_templates["Speaker-Based"] = createSpeakerBasedTemplate();
    m_templateNames.append("Speaker-Based");
    
    // Content-type template
    m_templates["Content-Type"] = createContentTypeTemplate();
    m_templateNames.append("Content-Type");
    
    // Broadcast template
    m_templates["Broadcast"] = createBroadcastTemplate();
    m_templateNames.append("Broadcast");
}

TrackOrganizer::TrackTemplate TrackOrganizer::createStandardTemplate() const
{
    TrackTemplate template_;
    template_.name = "Standard";
    template_.description = "Standard organization with basic content separation";
    template_.separateSpeakers = false;
    template_.groupSimilarContent = true;
    template_.maxTracksPerType = 50; // Increased from 3 to handle large AAF files
    template_.enableSmartPlacement = true;
    template_.preserveSpeakerConsistency = false;
    template_.conflictTolerance = 0.1; // 100ms tolerance

    // Track rules
    template_.trackRules["Speech"] = "Dialog";
    template_.trackRules["Music"] = "Music";
    template_.trackRules["SFX"] = "SoundFX";
    template_.trackRules["Ambience"] = "Ambience";
    template_.trackRules["Silence"] = "Silence";
    template_.trackRules["Unknown"] = "Unclassified";

    return template_;
}

TrackOrganizer::TrackTemplate TrackOrganizer::createSpeakerBasedTemplate() const
{
    TrackTemplate template_;
    template_.name = "Speaker-Based";
    template_.description = "Separate tracks for each speaker with intelligent assignment";
    template_.separateSpeakers = true;
    template_.enableSmartPlacement = true;
    template_.preserveSpeakerConsistency = true;
    template_.conflictTolerance = 0.05; // 50ms tolerance for speaker tracks
    template_.groupSimilarContent = false;
    template_.maxTracksPerType = 100; // Increased from 10 to handle large AAF files with many speakers
    
    // Track rules with speaker separation
    template_.trackRules["Speech"] = "Dia_{speaker}";
    template_.trackRules["Music"] = "Music";
    template_.trackRules["SFX"] = "SoundFX";
    template_.trackRules["Ambience"] = "Ambience";
    
    return template_;
}

TrackOrganizer::TrackTemplate TrackOrganizer::createContentTypeTemplate() const
{
    TrackTemplate template_;
    template_.name = "Content-Type";
    template_.description = "Detailed content type separation with overflow handling";
    template_.separateSpeakers = false;
    template_.groupSimilarContent = false;
    template_.maxTracksPerType = 75; // Increased from 3 to handle large AAF files
    template_.enableSmartPlacement = false;
    template_.preserveSpeakerConsistency = false;
    template_.conflictTolerance = 0.0; // No tolerance for content-type separation

    // Detailed track rules with overflow naming
    template_.trackRules["Speech"] = "Dialog";
    template_.trackRules["Music"] = "Music";
    template_.trackRules["SFX"] = "SFX";
    template_.trackRules["Ambience"] = "Ambience";
    template_.trackRules["Silence"] = "Silence";
    template_.trackRules["Unknown"] = "Unclassified";

    return template_;
}

TrackOrganizer::TrackTemplate TrackOrganizer::createBroadcastTemplate() const
{
    TrackTemplate template_;
    template_.name = "Broadcast";
    template_.description = "Professional broadcast layout with industry-standard naming";
    template_.separateSpeakers = true;
    template_.groupSimilarContent = true;
    template_.maxTracksPerType = 80; // Increased from 8 to handle large AAF files
    template_.enableSmartPlacement = true;
    template_.preserveSpeakerConsistency = true;
    template_.conflictTolerance = 0.02; // 20ms tolerance for broadcast

    // Broadcast-specific track rules with professional naming
    template_.trackRules["Speech"] = "VO_{speaker}";
    template_.trackRules["Music"] = "MX";
    template_.trackRules["SFX"] = "FX";
    template_.trackRules["Ambience"] = "ATMOS";
    template_.trackRules["Silence"] = "FILL";
    template_.trackRules["Unknown"] = "MISC";

    return template_;
}

TrackOrganizer::TrackAssignment TrackOrganizer::assignRegionToTrack(const QVariantMap &region,
                                                                   const TrackTemplate &trackTemplate,
                                                                   const QList<TrackAssignment> &existingAssignments) const
{
    TrackAssignment assignment;
    assignment.regionId = region.value("id").toString();
    assignment.originalTrack = region.value("track").toString();
    assignment.contentType = region.value("contentType", "Unknown").toString();
    assignment.speakerId = region.value("speakerId").toString();
    assignment.startTime = region.value("startTime").toDouble();
    assignment.duration = region.value("duration").toDouble();
    assignment.hasConflict = false;
    
    // Generate track name
    assignment.assignedTrack = generateTrackName(region, trackTemplate);
    
    // Check for conflicts
    assignment.hasConflict = hasTimingConflict(assignment, existingAssignments);
    if (assignment.hasConflict) {
        assignment.conflictReason = "Timing overlap detected";
    }
    
    return assignment;
}

QString TrackOrganizer::generateTrackName(const QVariantMap &region, const TrackTemplate &trackTemplate, int trackIndex) const
{
    QString contentType = region.value("contentType", "Unknown").toString();
    QString speakerId = region.value("speakerId").toString();
    
    QString baseName = trackTemplate.trackRules.value(contentType, "Unclassified").toString();

    // Handle speaker substitution
    if (trackTemplate.separateSpeakers && !speakerId.isEmpty()) {
        baseName = baseName.replace("{speaker}", speakerId);
    }
    
    // Add index if needed
    if (trackIndex > 0) {
        baseName += QString("_%1").arg(trackIndex + 1);
    }
    
    return baseName;
}

bool TrackOrganizer::hasTimingConflict(const TrackAssignment &assignment,
                                      const QList<TrackAssignment> &existingAssignments) const
{
    if (!m_preserveOriginalTiming) {
        return false;
    }
    
    double assignmentEnd = assignment.startTime + assignment.duration;
    
    for (const auto &existing : existingAssignments) {
        if (existing.assignedTrack == assignment.assignedTrack) {
            double existingEnd = existing.startTime + existing.duration;
            
            // Check for overlap
            if (!(assignmentEnd <= existing.startTime || assignment.startTime >= existingEnd)) {
                return true;
            }
        }
    }
    
    return false;
}

TrackOrganizer::TrackAssignment TrackOrganizer::resolveConflict(const TrackAssignment &conflictedAssignment,
                                                               const QList<TrackAssignment> &existingAssignments) const
{
    TrackAssignment resolved = conflictedAssignment;
    
    // Find available track name
    QString baseName = resolved.assignedTrack;
    resolved.assignedTrack = findAvailableTrackName(baseName, existingAssignments);
    resolved.hasConflict = false;
    resolved.conflictReason.clear();
    
    return resolved;
}

QString TrackOrganizer::findAvailableTrackName(const QString &baseName,
                                              const QList<TrackAssignment> &existingAssignments) const
{
    QString availableName = baseName;
    int suffix = 1;
    
    while (true) {
        bool nameInUse = false;
        
        for (const auto &assignment : existingAssignments) {
            if (assignment.assignedTrack == availableName) {
                nameInUse = true;
                break;
            }
        }
        
        if (!nameInUse) {
            break;
        }
        
        suffix++;
        availableName = QString("%1_%2").arg(baseName).arg(suffix);
    }
    
    return availableName;
}

QVariantMap TrackOrganizer::assignmentToVariantMap(const TrackAssignment &assignment) const
{
    QVariantMap map;
    map["regionId"] = assignment.regionId;
    map["regionName"] = assignment.regionName;
    map["originalTrack"] = assignment.originalTrack;
    map["assignedTrack"] = assignment.assignedTrack;
    map["contentType"] = assignment.contentType;
    map["speakerId"] = assignment.speakerId;
    map["startTime"] = assignment.startTime;
    map["duration"] = assignment.duration;
    map["hasConflict"] = assignment.hasConflict;
    map["conflictReason"] = assignment.conflictReason;
    map["confidence"] = assignment.confidence;
    map["trackIndex"] = assignment.trackIndex;
    return map;
}

TrackOrganizer::TrackAssignment TrackOrganizer::variantMapToAssignment(const QVariantMap &map) const
{
    TrackAssignment assignment;
    assignment.regionId = map.value("regionId").toString();
    assignment.regionName = map.value("regionName").toString();
    assignment.originalTrack = map.value("originalTrack").toString();
    assignment.assignedTrack = map.value("assignedTrack").toString();
    assignment.contentType = map.value("contentType").toString();
    assignment.speakerId = map.value("speakerId").toString();
    assignment.startTime = map.value("startTime").toDouble();
    assignment.duration = map.value("duration").toDouble();
    assignment.hasConflict = map.value("hasConflict").toBool();
    assignment.conflictReason = map.value("conflictReason").toString();
    assignment.confidence = map.value("confidence").toDouble();
    assignment.trackIndex = map.value("trackIndex").toInt();
    return assignment;
}

void TrackOrganizer::saveTemplates()
{
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::ConfigLocation);
    QDir().mkpath(configPath + "/WAAFer");
    
    QSettings settings(configPath + "/WAAFer/track_templates.ini", QSettings::IniFormat);
    
    settings.beginWriteArray("templates");
    int index = 0;
    
    for (auto it = m_templates.begin(); it != m_templates.end(); ++it) {
        settings.setArrayIndex(index++);
        settings.setValue("name", it.value().name);
        settings.setValue("description", it.value().description);
        // Save other template properties as needed
    }
    
    settings.endArray();
    settings.sync();
}

void TrackOrganizer::loadTemplates()
{
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::ConfigLocation);
    QSettings settings(configPath + "/WAAFer/track_templates.ini", QSettings::IniFormat);
    
    int size = settings.beginReadArray("templates");
    for (int i = 0; i < size; ++i) {
        settings.setArrayIndex(i);
        QString name = settings.value("name").toString();
        QString description = settings.value("description").toString();
        
        // Load custom templates if they don't exist
        if (!m_templates.contains(name) && !name.isEmpty()) {
            TrackTemplate template_;
            template_.name = name;
            template_.description = description;
            // Load other properties as needed
            
            m_templates[name] = template_;
            if (!m_templateNames.contains(name)) {
                m_templateNames.append(name);
            }
        }
    }
    settings.endArray();
}

QVariantList TrackOrganizer::optimizeOrganization(const QVariantList &regions)
{
    if (!m_performanceOptimizations) {
        QVariantList emptyTracks;
        return organizeRegions(regions, emptyTracks);
    }

    // Generate cache key
    QString cacheKey = QString::number(qHash(QJsonDocument::fromVariant(regions).toJson()));

    // Check cache first
    if (m_organizationCache.contains(cacheKey)) {
        qDebug() << "Using cached organization for" << regions.size() << "regions";
        return m_organizationCache[cacheKey];
    }

    qDebug() << "Optimizing organization for" << regions.size() << "regions";

    // Sort regions by start time for better processing
    QVariantList sortedRegions = regions;
    std::sort(sortedRegions.begin(), sortedRegions.end(), [](const QVariant &a, const QVariant &b) {
        double startA = a.toMap().value("startTime", a.toMap().value("position", 0.0)).toDouble();
        double startB = b.toMap().value("startTime", b.toMap().value("position", 0.0)).toDouble();
        return startA < startB;
    });

    // Process with current template (preserves template selection)
    QVariantList emptyTracks;
    QVariantList result = organizeRegions(sortedRegions, emptyTracks);

    // Cache result
    m_organizationCache[cacheKey] = result;

    qDebug() << "Organization optimized and cached";
    return result;
}

QVariantList TrackOrganizer::updateOrganization(const QVariantList &updatedRegions)
{
    qDebug() << "Updating organization with" << updatedRegions.size() << "updated regions";

    // Clear relevant cache entries
    if (m_performanceOptimizations) {
        m_organizationCache.clear();
    }

    // Reset track counters for fresh assignment
    m_trackCounters.clear();

    return optimizeOrganization(updatedRegions);
}

QVariantMap TrackOrganizer::getDetailedAnalytics(const QVariantList &assignments) const
{
    QVariantMap analytics = getOrganizationStats(assignments);

    // Add speaker-specific analytics
    if (!m_speakerDatabase.isEmpty()) {
        QVariantMap speakerAnalytics;
        QMap<QString, QVariantList> speakerTracks;
        QMap<QString, double> speakerDurations;

        for (const QVariant &assignmentVar : assignments) {
            QVariantMap assignment = assignmentVar.toMap();
            QString speakerId = assignment.value("speakerId").toString();
            QString trackName = assignment.value("assignedTrack").toString();
            double duration = assignment.value("duration", 0.0).toDouble();

            if (!speakerId.isEmpty()) {
                if (!speakerTracks.contains(speakerId)) {
                    speakerTracks[speakerId] = QVariantList();
                }
                if (!speakerTracks[speakerId].contains(trackName)) {
                    speakerTracks[speakerId].append(trackName);
                }
                speakerDurations[speakerId] += duration;
            }
        }

        QVariantMap speakerTrackMap;
        QVariantMap speakerDurationMap;
        for (auto it = speakerTracks.begin(); it != speakerTracks.end(); ++it) {
            speakerTrackMap[it.key()] = it.value();
        }
        for (auto it = speakerDurations.begin(); it != speakerDurations.end(); ++it) {
            speakerDurationMap[it.key()] = it.value();
        }

        speakerAnalytics["speakerTracks"] = speakerTrackMap;
        speakerAnalytics["speakerDurations"] = speakerDurationMap;
        speakerAnalytics["uniqueSpeakers"] = speakerTracks.size();

        analytics["speakerAnalytics"] = speakerAnalytics;
    }

    // Add track efficiency metrics
    QList<TrackAssignment> assignmentList;
    for (const QVariant &assignmentVar : assignments) {
        assignmentList.append(variantMapToAssignment(assignmentVar.toMap()));
    }
    analytics["trackEfficiency"] = calculateTrackEfficiency(assignmentList);

    return analytics;
}

TrackOrganizer::TrackAssignment TrackOrganizer::assignRegionToTrackWithTemplate(
    const QVariantMap &region,
    const TrackTemplate &trackTemplate,
    const QList<TrackAssignment> &existingAssignments) const
{
    TrackAssignment assignment;
    assignment.regionId = region.value("id").toString();
    assignment.originalTrack = region.value("track").toString();
    assignment.contentType = region.value("contentType", "Unknown").toString();
    assignment.speakerId = region.value("speakerId").toString();
    assignment.startTime = region.value("startTime").toDouble();
    assignment.duration = region.value("duration").toDouble();
    assignment.confidence = region.value("confidence", 0.5).toDouble();
    assignment.regionName = region.value("name", assignment.regionId).toString();
    assignment.hasConflict = false;

    // Template-specific track assignment logic
    QString contentType = assignment.contentType;
    QString speakerId = assignment.speakerId;

    // Get base track name from template rules
    QString baseTrackName = trackTemplate.trackRules.value(contentType, "Unclassified").toString();

    // Apply template-specific logic
    if (trackTemplate.name == "Standard") {
        // Standard: Basic content separation with multiple tracks per type
        assignment.assignedTrack = baseTrackName;

        // Count existing tracks of the same content type
        int trackCount = 0;
        for (const TrackAssignment &existing : existingAssignments) {
            if (existing.assignedTrack.startsWith(baseTrackName)) {
                trackCount++;
            }
        }

        qDebug() << "Standard template track assignment - Content:" << contentType
                 << "Base:" << baseTrackName << "Existing count:" << trackCount
                 << "Max per type:" << trackTemplate.maxTracksPerType;

        // Apply maxTracksPerType logic for Standard template
        if (trackCount > 0 && trackCount < trackTemplate.maxTracksPerType) {
            // Add index for multiple tracks of same type (Dialog_1, Dialog_2, etc.)
            assignment.assignedTrack = QString("%1_%2").arg(baseTrackName).arg(trackCount + 1);
            qDebug() << "Created indexed track:" << assignment.assignedTrack;
        } else if (trackCount >= trackTemplate.maxTracksPerType) {
            // Create overflow track with letter suffix (Dialog_A, Dialog_B, etc.)
            char suffix = 'A' + (trackCount / trackTemplate.maxTracksPerType);
            assignment.assignedTrack = QString("%1_%2").arg(baseTrackName).arg(suffix);
            qDebug() << "Created overflow track:" << assignment.assignedTrack;
        } else {
            qDebug() << "Using base track:" << assignment.assignedTrack;
        }

    } else if (trackTemplate.name == "Speaker-Based") {
        // Speaker-Based: Separate tracks per speaker
        if (trackTemplate.separateSpeakers && !speakerId.isEmpty() && contentType.contains("Speech", Qt::CaseInsensitive)) {
            assignment.assignedTrack = baseTrackName.replace("{speaker}", speakerId);
        } else {
            assignment.assignedTrack = baseTrackName;
        }

    } else if (trackTemplate.name == "Content-Type") {
        // Content-Type: Detailed content separation with overflow handling
        assignment.assignedTrack = baseTrackName;

        // Add overflow handling for content types
        int trackCount = 0;
        for (const TrackAssignment &existing : existingAssignments) {
            if (existing.assignedTrack.startsWith(baseTrackName)) {
                trackCount++;
            }
        }

        if (trackCount >= trackTemplate.maxTracksPerType) {
            // Create overflow track
            char suffix = 'A' + (trackCount / trackTemplate.maxTracksPerType);
            assignment.assignedTrack = QString("%1_%2").arg(baseTrackName).arg(suffix);
        } else if (trackCount > 0) {
            // Add index for multiple tracks of same type
            assignment.assignedTrack = QString("%1_%2").arg(baseTrackName).arg(trackCount + 1);
        }

    } else if (trackTemplate.name == "Broadcast") {
        // Broadcast: Professional broadcast layout
        if (trackTemplate.separateSpeakers && !speakerId.isEmpty() && contentType.contains("Speech", Qt::CaseInsensitive)) {
            assignment.assignedTrack = baseTrackName.replace("{speaker}", speakerId);
        } else {
            assignment.assignedTrack = baseTrackName;
        }

        // Add broadcast-specific numbering
        int broadcastIndex = 1;
        for (const TrackAssignment &existing : existingAssignments) {
            if (existing.assignedTrack.startsWith(assignment.assignedTrack.split("_").first())) {
                broadcastIndex++;
            }
        }

        if (broadcastIndex > 1) {
            assignment.assignedTrack += QString("_%1").arg(broadcastIndex);
        }
    } else {
        // Custom template - use basic assignment
        assignment.assignedTrack = baseTrackName;
        if (trackTemplate.separateSpeakers && !speakerId.isEmpty()) {
            assignment.assignedTrack = assignment.assignedTrack.replace("{speaker}", speakerId);
        }
    }

    // Check for conflicts
    assignment.hasConflict = checkForConflicts(assignment, existingAssignments);

    qDebug() << "Template assignment:" << trackTemplate.name
             << "Region:" << assignment.regionId
             << "Content:" << contentType
             << "Speaker:" << speakerId
             << "→ Track:" << assignment.assignedTrack;

    return assignment;
}

TrackOrganizer::TrackAssignment TrackOrganizer::assignRegionToTrackEnhanced(
    const QVariantMap &region,
    const TrackTemplate &trackTemplate,
    const QList<TrackAssignment> &existingAssignments) const
{
    // Start with basic assignment
    TrackAssignment assignment = assignRegionToTrack(region, trackTemplate, existingAssignments);

    // Enhance with speaker intelligence
    QString speakerId = region.value("speakerId").toString();
    QString contentType = region.value("contentType", region.value("classification")).toString();

    if (!speakerId.isEmpty() && contentType.contains("Speech", Qt::CaseInsensitive)) {
        // Use speaker database for intelligent track assignment
        QVariantList speakers = m_speakerDatabase.value("speakers").toList();

        for (const QVariant &speakerVar : speakers) {
            QVariantMap speaker = speakerVar.toMap();
            if (speaker.value("speakerId").toString() == speakerId) {
                // Found speaker in database, use enhanced naming
                int regionCount = speaker.value("regionCount", 1).toInt();
                double confidence = speaker.value("averageConfidence", 0.5).toDouble();

                // Adjust track name based on speaker characteristics
                if (confidence > 0.8 && regionCount > 3) {
                    // High confidence, frequent speaker - use primary track
                    assignment.assignedTrack = QString("Dia_%1").arg(speakerId);
                } else if (confidence > 0.6) {
                    // Medium confidence - use secondary track
                    assignment.assignedTrack = QString("Dia_%1_Alt").arg(speakerId);
                } else {
                    // Low confidence - use review track
                    assignment.assignedTrack = QString("Dia_%1_Review").arg(speakerId);
                }

                assignment.confidence = confidence;
                break;
            }
        }

        // Check for optimal placement
        QString optimalTrack = findOptimalTrackPlacement(assignment, existingAssignments);
        if (!optimalTrack.isEmpty()) {
            assignment.assignedTrack = optimalTrack;
        }
    }

    return assignment;
}

TrackOrganizer::TrackAssignment TrackOrganizer::resolveConflictSmart(
    const TrackAssignment &conflictedAssignment,
    const QList<TrackAssignment> &existingAssignments) const
{
    TrackAssignment resolved = conflictedAssignment;

    // Smart conflict resolution based on speaker and content analysis
    QString speakerId = resolved.speakerId;
    QString contentType = resolved.contentType;

    // Find all assignments for this speaker
    QList<TrackAssignment> speakerAssignments;
    for (const TrackAssignment &existing : existingAssignments) {
        if (existing.speakerId == speakerId && existing.regionId != resolved.regionId) {
            speakerAssignments.append(existing);
        }
    }

    // If speaker has consistent track usage, try to maintain it
    if (!speakerAssignments.isEmpty()) {
        QMap<QString, int> trackUsage;
        for (const TrackAssignment &speakerAssign : speakerAssignments) {
            trackUsage[speakerAssign.assignedTrack]++;
        }

        // Find most used track for this speaker
        QString preferredTrack;
        int maxUsage = 0;
        for (auto it = trackUsage.begin(); it != trackUsage.end(); ++it) {
            if (it.value() > maxUsage) {
                maxUsage = it.value();
                preferredTrack = it.key();
            }
        }

        // Check if preferred track has space
        bool hasConflict = false;
        for (const TrackAssignment &existing : existingAssignments) {
            if (existing.assignedTrack == preferredTrack && existing.regionId != resolved.regionId) {
                // Check for time overlap
                double existingStart = existing.startTime;
                double existingEnd = existing.startTime + existing.duration;
                double resolvedStart = resolved.startTime;
                double resolvedEnd = resolved.startTime + resolved.duration;

                if (!(resolvedEnd <= existingStart || resolvedStart >= existingEnd)) {
                    hasConflict = true;
                    break;
                }
            }
        }

        if (!hasConflict) {
            resolved.assignedTrack = preferredTrack;
            resolved.hasConflict = false;
            resolved.conflictReason = "";
            return resolved;
        }
    }

    // Fall back to creating new track with intelligent naming
    QString baseTrack = resolved.assignedTrack;
    if (baseTrack.contains("_")) {
        baseTrack = baseTrack.split("_").first();
    }

    // Find next available track number
    int trackNumber = 1;
    QString newTrack;
    do {
        if (!speakerId.isEmpty() && contentType.contains("Speech", Qt::CaseInsensitive)) {
            newTrack = QString("%1_%2_%3").arg(baseTrack).arg(speakerId).arg(trackNumber);
        } else {
            newTrack = QString("%1_%2").arg(baseTrack).arg(QChar('A' + trackNumber - 1));
        }
        trackNumber++;
    } while (trackExists(newTrack, existingAssignments) && trackNumber < 26);

    resolved.assignedTrack = newTrack;
    resolved.hasConflict = false;
    resolved.conflictReason = QString("Resolved to new track: %1").arg(newTrack);

    return resolved;
}

QString TrackOrganizer::findOptimalTrackPlacement(
    const TrackAssignment &assignment,
    const QList<TrackAssignment> &existingAssignments) const
{
    QString speakerId = assignment.speakerId;
    QString contentType = assignment.contentType;

    // For speech content, try to group by speaker
    if (!speakerId.isEmpty() && contentType.contains("Speech", Qt::CaseInsensitive)) {
        // Find existing tracks for this speaker
        QStringList speakerTracks;
        for (const TrackAssignment &existing : existingAssignments) {
            if (existing.speakerId == speakerId && !speakerTracks.contains(existing.assignedTrack)) {
                speakerTracks.append(existing.assignedTrack);
            }
        }

        // Check each speaker track for availability
        for (const QString &track : speakerTracks) {
            bool hasConflict = false;
            for (const TrackAssignment &existing : existingAssignments) {
                if (existing.assignedTrack == track) {
                    // Check for time overlap
                    double existingStart = existing.startTime;
                    double existingEnd = existing.startTime + existing.duration;
                    double assignmentStart = assignment.startTime;
                    double assignmentEnd = assignment.startTime + assignment.duration;

                    if (!(assignmentEnd <= existingStart || assignmentStart >= existingEnd)) {
                        hasConflict = true;
                        break;
                    }
                }
            }

            if (!hasConflict) {
                return track; // Found available speaker track
            }
        }
    }

    return QString(); // No optimal placement found
}

QVariantMap TrackOrganizer::calculateTrackEfficiency(const QList<TrackAssignment> &assignments) const
{
    QVariantMap efficiency;

    // Calculate track utilization
    QMap<QString, double> trackDurations;
    QMap<QString, int> trackRegionCounts;
    double totalDuration = 0.0;

    for (const TrackAssignment &assignment : assignments) {
        trackDurations[assignment.assignedTrack] += assignment.duration;
        trackRegionCounts[assignment.assignedTrack]++;
        totalDuration += assignment.duration;
    }

    // Calculate efficiency metrics
    int totalTracks = trackDurations.size();
    double averageDurationPerTrack = totalTracks > 0 ? totalDuration / totalTracks : 0.0;

    // Find track utilization distribution
    QVariantMap trackUtilization;
    double maxTrackDuration = 0.0;
    double minTrackDuration = std::numeric_limits<double>::max();

    for (auto it = trackDurations.begin(); it != trackDurations.end(); ++it) {
        double duration = it.value();
        trackUtilization[it.key()] = duration;
        maxTrackDuration = qMax(maxTrackDuration, duration);
        minTrackDuration = qMin(minTrackDuration, duration);
    }

    // Calculate efficiency score (0-100)
    double utilizationVariance = totalTracks > 1 ? (maxTrackDuration - minTrackDuration) / averageDurationPerTrack : 0.0;
    double efficiencyScore = qMax(0.0, 100.0 - (utilizationVariance * 10.0));

    efficiency["totalTracks"] = totalTracks;
    efficiency["totalDuration"] = totalDuration;
    efficiency["averageDurationPerTrack"] = averageDurationPerTrack;
    efficiency["maxTrackDuration"] = maxTrackDuration;
    efficiency["minTrackDuration"] = minTrackDuration;
    efficiency["efficiencyScore"] = efficiencyScore;
    efficiency["trackUtilization"] = trackUtilization;

    // Speaker-specific efficiency
    if (!m_speakerDatabase.isEmpty()) {
        QMap<QString, int> speakerTrackCounts;
        for (const TrackAssignment &assignment : assignments) {
            if (!assignment.speakerId.isEmpty()) {
                speakerTrackCounts[assignment.speakerId]++;
            }
        }

        QVariantMap speakerEfficiency;
        for (auto it = speakerTrackCounts.begin(); it != speakerTrackCounts.end(); ++it) {
            speakerEfficiency[it.key()] = it.value();
        }
        efficiency["speakerTrackDistribution"] = speakerEfficiency;
    }

    return efficiency;
}

// Helper method to check if track exists
bool TrackOrganizer::trackExists(const QString &trackName, const QList<TrackAssignment> &assignments) const
{
    for (const TrackAssignment &assignment : assignments) {
        if (assignment.assignedTrack == trackName) {
            return true;
        }
    }
    return false;
}

bool TrackOrganizer::checkForConflicts(const TrackAssignment &assignment, const QList<TrackAssignment> &existingAssignments) const
{
    for (const TrackAssignment &existing : existingAssignments) {
        // Only check conflicts on the same track
        if (existing.assignedTrack == assignment.assignedTrack) {
            // Check for time overlap
            double existingStart = existing.startTime;
            double existingEnd = existing.startTime + existing.duration;
            double assignmentStart = assignment.startTime;
            double assignmentEnd = assignment.startTime + assignment.duration;

            // Check if there's an overlap (with small tolerance)
            double tolerance = 0.01; // 10ms tolerance
            if (!(assignmentEnd <= existingStart + tolerance || assignmentStart >= existingEnd - tolerance)) {
                return true; // Conflict found
            }
        }
    }
    return false; // No conflicts
}
