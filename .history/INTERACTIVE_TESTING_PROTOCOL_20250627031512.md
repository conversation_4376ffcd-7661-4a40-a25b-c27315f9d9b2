# WAAFer Timeline Fixes - Interactive Testing Protocol

## 🎯 **TESTING STATUS: APPLICATION LAUNCH REPORTED**

**Application reported as running and ready for testing**
**AAF file loaded: 64 tracks, 700 regions, 95.84 seconds**
**Stereo pairs detected: PGM L/R, VODITELJA, SFX, GLASBA, MATRICA L/R, etc.**

---

## 📋 **SYSTEMATIC TESTING CHECKLIST**

### **TEST 1: Region Name Display Toggle (🔄 Button)**

**What to Test:** Toggle between Source A (file names) and Source B (descriptions)

**Steps:**
1. **Navigate to Timeline Tab** (should be the first tab)
2. **Look for the timeline toolbar** at the top of the timeline view
3. **Find the 🔄 (toggle) button** in the toolbar
4. **Click the 🔄 button** to toggle region name sources
5. **Observe region names change** between:
   - **Source A**: File-based names (e.g., technical IDs)
   - **Source B**: Descriptive names (e.g., metadata descriptions)

**Expected Results:**
- Toggle button is visible and clickable
- Region names change when clicking toggle
- Tooltip updates to show current source (A or B)
- Names are different between the two sources

**Report any issues:**
- [ ] Toggle button not visible
- [ ] Toggle button not working
- [ ] Names don't change when toggling
- [ ] Both sources show identical names

---

### **TEST 2: Stereo Audio Channel Display**

**What to Test:** Both left and right channels of stereo pairs are visible

**Steps:**
1. **Stay in Timeline Tab**
2. **Look for stereo track pairs** in the track list:
   - **PGM L / IZJAVA** (Track 1) + **PGM R / IZJAVA** (Track 2)
   - **VODITELJA** (Track 3) + **VODITELJA** (Track 4)
   - **MATRICA L** (Track 27) + **MATRICA R** (Track 28)
   - **AMBI HALL L** (Track 45) + **AMBI HALL R** (Track 46)
3. **Verify both channels are visible** for each stereo pair
4. **Check that regions appear on both tracks** of each stereo pair

**Expected Results:**
- Both left and right channel tracks are visible
- Regions appear on both tracks of stereo pairs
- Track names clearly indicate L/R or stereo pairing
- No missing channels in stereo pairs

**Report any issues:**
- [ ] Only left channels visible
- [ ] Only right channels visible
- [ ] Missing regions on one channel
- [ ] Stereo pairs not properly grouped

---

### **TEST 3: Timeline Scrolling Without Visual Artifacts**

**What to Test:** Smooth scrolling and zooming without graphics breaking apart

**Steps:**
1. **Test Vertical Scrolling:**
   - Use mouse wheel or scrollbar to scroll up/down through tracks
   - Look for any visual glitches, clipping, or artifacts
2. **Test Horizontal Scrolling:**
   - Scroll left/right through the timeline
   - Check for proper region rendering
3. **Test Zooming:**
   - Use zoom controls (+ / - buttons) if available
   - Test zoom in and zoom out operations
4. **Test Combined Operations:**
   - Scroll while zoomed in
   - Change zoom levels while scrolled

**Expected Results:**
- Smooth vertical scrolling without artifacts
- Smooth horizontal scrolling without artifacts
- Clean zooming operations
- No graphics breaking apart or clipping through boundaries
- Proper region rendering at all zoom levels

**Report any issues:**
- [ ] Visual artifacts during scrolling
- [ ] Graphics breaking apart
- [ ] Clipping through window boundaries
- [ ] Jerky or stuttering scrolling
- [ ] Zoom operations causing visual problems

---

### **TEST 4: Organization Tab Timeline Integration**

**What to Test:** Organization tab has both table and timeline views

**Steps:**
1. **Navigate to Organization Tab** (should be third tab)
2. **Look for tabbed interface** within the Organization tab
3. **Verify two sub-tabs exist:**
   - **"Table View"** - Traditional table display
   - **"Timeline View"** - Timeline display of organized tracks
4. **Click between the two sub-tabs** to test switching
5. **Test organization preview:**
   - Use organization controls to preview track organization
   - Check if timeline view updates with organized tracks

**Expected Results:**
- Organization tab contains tabbed interface
- "Table View" and "Timeline View" sub-tabs exist
- Can switch between table and timeline views
- Timeline view shows organized tracks when organization is applied
- Both views update when organization changes

**Report any issues:**
- [ ] No tabbed interface in Organization tab
- [ ] Missing "Timeline View" sub-tab
- [ ] Cannot switch between views
- [ ] Timeline view doesn't show organized tracks
- [ ] Views don't update with organization changes

---

### **TEST 5: Export Process (Large Dataset Handling)**

**What to Test:** Export handles 700 regions without "Argument list too long" error

**Steps:**
1. **Navigate to Export Tab** (should be fourth tab)
2. **Set up basic export parameters:**
   - Choose output file location
   - Select AAF export format
3. **Attempt to start export process**
4. **Monitor for errors** in the application or console
5. **Check export progress** (don't need to complete full export)

**Expected Results:**
- Export process starts without "Argument list too long" error
- No command line argument errors
- Export progress begins normally
- Temporary files are created and managed properly
- Large dataset (700 regions) is handled correctly

**Report any issues:**
- [ ] "Argument list too long" error appears
- [ ] Export fails to start
- [ ] Command line errors in console
- [ ] Export process crashes
- [ ] Memory or performance issues with large dataset

---

## 🔍 **ADDITIONAL VERIFICATION TESTS**

### **General Functionality Check:**
- [ ] All tabs are accessible and functional
- [ ] Application remains stable during testing
- [ ] No crashes or freezes occur
- [ ] Memory usage remains reasonable
- [ ] All Phase 1-5 functionality still works

### **Performance Check:**
- [ ] Timeline responds quickly to user interactions
- [ ] Scrolling is smooth and responsive
- [ ] Tab switching is fast
- [ ] AAF loading completed in reasonable time
- [ ] No significant lag or delays

---

## 📝 **REPORTING INSTRUCTIONS**

**For each test, please report:**

1. **✅ PASS** - If the test works as expected
2. **❌ FAIL** - If the test fails, with specific details:
   - What exactly went wrong?
   - When did it happen (which step)?
   - Any error messages seen?
   - Screenshots if helpful

3. **⚠️ PARTIAL** - If the test mostly works but has minor issues

**Example Report Format:**
```
TEST 1 (Region Name Toggle): ✅ PASS
- Toggle button visible and working
- Names change between sources correctly
- Tooltip updates properly

TEST 2 (Stereo Display): ❌ FAIL
- Only left channels visible for VODITELJA tracks
- Right channel (Track 4) not showing regions
```

---

## 🎯 **SUCCESS CRITERIA**

**All five tests must PASS for the fixes to be considered complete:**

1. ✅ Region name toggle working
2. ✅ Stereo channels displaying properly  
3. ✅ Timeline scrolling without artifacts
4. ✅ Organization tab timeline integration functional
5. ✅ Export handling large datasets without errors

**Only when ALL tests pass will the timeline fixes be considered fully resolved.**
