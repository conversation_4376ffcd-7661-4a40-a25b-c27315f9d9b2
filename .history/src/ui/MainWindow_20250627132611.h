#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QLabel>
#include <QPushButton>
#include <QProgressBar>
#include <QTextEdit>
#include <QSplitter>
#include <QGroupBox>
#include <QGridLayout>
#include <QFileDialog>
#include <QMessageBox>
#include <QTimer>
#include <QDebug>
#include <QTabWidget>
#include <QCheckBox>
#include <QComboBox>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QTableWidget>
#include <QHeaderView>
#include <QTableWidgetItem>
#include <QCloseEvent>
#include <QRadioButton>
#include <QSettings>
#include <QNetworkAccessManager>
#include "AnalysisSettingsDialog.h"
#include <QNetworkRequest>
#include <QNetworkReply>

// Forward declarations
class AAFReader;
class MemoryManager;
class PythonBridge;
class LibAAFWrapper;
class AudioAnalyzer;
class AudioFileManager;
class ProgressTracker;
class AnalysisSettings;
class ClassificationEngine;
class TrackOrganizer;
class LMStudioClient;
class AudioPlaybackManager;
class AAFExporter;
class TimelineWidget;
class QRadioButton;
class ClassificationReviewDialog;

/**
 * @brief Main window for WAAFer application
 * 
 * This class provides the main user interface for the WAAFer
 * AI-powered AAF audio organizer application.
 */
class MainWindow : public QMainWindow
{
    Q_OBJECT
    
public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();
    
    /**
     * @brief Set core components
     * @param aafReader AAF reader instance
     * @param memoryManager Memory manager instance
     * @param pythonBridge Python bridge instance
     * @param libAAFWrapper LibAAF wrapper instance
     */
    void setCoreComponents(AAFReader *aafReader,
                          MemoryManager *memoryManager,
                          PythonBridge *pythonBridge,
                          LibAAFWrapper *libAAFWrapper,
                          AudioAnalyzer *audioAnalyzer,
                          AudioFileManager *audioFileManager,
                          ProgressTracker *progressTracker,
                          AnalysisSettings *analysisSettings,
                          ClassificationEngine *classificationEngine,
                          TrackOrganizer *trackOrganizer,
                          LMStudioClient *lmStudioClient,
                          AudioPlaybackManager *audioPlaybackManager,
                          AAFExporter *aafExporter);
    
protected:
    /**
     * @brief Handle close event
     * @param event Close event
     */
    void closeEvent(QCloseEvent *event) override;

    /**
     * @brief Check if mock data generation is disabled
     * @return true if mock data should be disabled
     */
    bool isMockDataDisabled() const { return m_settingsData.disableMockData; }
    
public slots:
    /**
     * @brief Load AAF file with given path
     * @param filePath Path to AAF file to load
     */
    void loadAAFFile(const QString &filePath);

    /**
     * @brief Force window to be visible and on top
     */
    void forceWindowVisible();

    /**
     * @brief Create a simple test window for debugging GUI visibility
     */
    static QWidget* createTestWindow();

private slots:
    /**
     * @brief Open AAF file dialog
     */
    void openAAFFile();

    /**
     * @brief Close current AAF file
     */
    void closeAAFFile();
    
    /**
     * @brief Test Python integration
     */
    void testPythonIntegration();
    
    /**
     * @brief Test audio libraries
     */
    void testAudioLibraries();
    
    /**
     * @brief Show memory statistics
     */
    void showMemoryStats();
    
    /**
     * @brief Update status information
     */
    void updateStatus();
    
    /**
     * @brief Handle AAF file load completion
     * @param success Whether loading was successful
     * @param message Success or error message
     */
    void onAAFFileLoadCompleted(bool success, const QString &message);
    
    /**
     * @brief Handle AAF parsing progress
     * @param progress Progress percentage
     * @param status Status message
     */
    void onAAFParsingProgress(int progress, const QString &status);
    
    /**
     * @brief Handle Python operation completion
     * @param success Whether operation was successful
     * @param message Result or error message
     */
    void onPythonOperationCompleted(bool success, const QString &message);
    
    /**
     * @brief Handle memory warning
     * @param usage Memory usage percentage
     * @param message Warning message
     */
    void onMemoryWarning(int usage, const QString &message);

    // Phase 3 slots
    /**
     * @brief Handle progress updates
     */
    void onProgressChanged();

    /**
     * @brief Handle progress task changes
     */
    void onProgressTaskChanged();

    /**
     * @brief Handle progress time updates
     */
    void onProgressTimeChanged();

    /**
     * @brief Pause current analysis
     */
    void pauseAnalysis();

    /**
     * @brief Stop current analysis
     */
    void stopAnalysis();

    /**
     * @brief Resume current analysis
     */
    void resumeAnalysis();

    /**
     * @brief Start classification process
     */
    void startClassification();

    /**
     * @brief Handle classification progress updates
     */
    void onClassificationProgressChanged();

    /**
     * @brief Handle classification completion
     */
    void onClassificationCompleted(const QVariantList &results);

    /**
     * @brief Handle classification error
     */
    void onClassificationError(const QString &regionId, const QString &error);

    /**
     * @brief Apply analysis settings
     */
    void applyAnalysisSettings();

    /**
     * @brief Reset analysis settings to defaults
     */
    void resetAnalysisSettings();

    /**
     * @brief Handle time range selection changes
     */
    void onTimeRangeChanged();

    /**
     * @brief Set time range from timeline selection
     */
    void setTimeRangeFromTimeline();

    /**
     * @brief Clear time range selection
     */
    void clearTimeRange();

    /**
     * @brief Handle export time range mode changes
     */
    void onExportTimeRangeModeChanged();

    /**
     * @brief Update export file name based on time range
     */
    void updateExportFileName();



    /**
     * @brief Preview track organization
     */
    void previewTrackOrganization();

    /**
     * @brief Apply track organization
     */
    void applyTrackOrganization();

    /**
     * @brief Open preset management dialog
     */
    void openPresetManagement();

    /**
     * @brief Update organization timeline with organized tracks
     */
    void updateOrganizationTimeline(const QVariantList &assignments);

    /**
     * @brief Handle organization completion
     */
    void onOrganizationCompleted(const QVariantList &assignments, const QVariantMap &stats);

    /**
     * @brief Open classification review dialog
     */
    void openClassificationReview();

    /**
     * @brief Handle updated classifications from review dialog
     */
    void onClassificationsUpdated(const QVariantList &updatedResults);

    /**
     * @brief Save classification data to file
     */
    void saveClassificationData();

    /**
     * @brief Load classification data from file
     */
    void loadClassificationData();

    /**
     * @brief Handle LLM provider selection change
     */
    void onLLMProviderChanged();

    /**
     * @brief Open LLM settings dialog
     */
    void openLLMSettings();

    /**
     * @brief Browse for export destination
     */
    void browseExportPath();
    void updateExportButtonState();

    /**
     * @brief Start export process
     */
    void startExport();

    /**
     * @brief Handle export format change
     */
    void onExportFormatChanged();

    /**
     * @brief Open analysis settings dialog
     */
    void openAnalysisSettings();

    // Phase 5 slots
    /**
     * @brief Handle timeline region selection
     */
    void onTimelineRegionSelected(const QString &regionId);

    /**
     * @brief Handle timeline region updates
     */
    void onTimelineRegionUpdated(const QString &regionId, const QVariantMap &metadata);

    /**
     * @brief Handle timeline playback requests
     */
    void onTimelinePlaybackRequested(const QString &regionId);

    /**
     * @brief Handle timeline time range selection
     */
    void onTimelineRangeSelected(double startTime, double endTime);

    /**
     * @brief Handle organization timeline region selection
     */
    void onOrganizationTimelineRegionSelected(const QString &regionId);

    /**
     * @brief Handle organization timeline region updates
     */
    void onOrganizationTimelineRegionUpdated(const QString &regionId, const QVariantMap &metadata);

    /**
     * @brief Handle organization table selection changes
     */
    void onOrganizationTableSelectionChanged();

    /**
     * @brief Handle classification table selection changes
     */
    void onClassificationTableSelectionChanged();

private:
    /**
     * @brief Format time in seconds to HH:MM:SS.mmm format
     * @param timeInSeconds Time in seconds
     * @return Formatted time string
     */
    QString formatTimecode(double timeInSeconds) const;

    /**
     * @brief Parse time string in HH:MM:SS.mmm format to seconds
     * @param timeString Time string to parse
     * @return Time in seconds, or 0.0 if invalid
     */
    double parseTimeString(const QString &timeString) const;



    // Analysis settings storage (since UI elements are now in dialog)
    struct AnalysisSettingsData {
        QString quality = "Balanced";
        int chunkSkipFactor = 1;
        int maxConcurrent = 2;
        bool fastMode = false;
        double confidenceThreshold = 0.6;
        bool speakerDiarization = true;
        bool musicDetection = true;
        bool sfxDetection = true;
        bool disableMockData = false;  // Disable all mock/fake data generation
    } m_settingsData;

    // Time range selection for analysis
    struct TimeRangeData {
        bool analyzeFullAAF = true;
        double startTime = 0.0;  // in seconds
        double endTime = 0.0;    // in seconds
        bool isValid() const { return !analyzeFullAAF && endTime > startTime; }
    } m_timeRangeData;

    // Export time range selection
    enum ExportTimeRangeMode {
        ExportFullAAF,
        ExportAnalysisRange,
        ExportCustomRange
    };

    struct ExportTimeRangeData {
        ExportTimeRangeMode mode = ExportFullAAF;
        double startTime = 0.0;  // in seconds
        double endTime = 0.0;    // in seconds
        bool isCustomRangeValid() const { return endTime > startTime; }
    } m_exportTimeRangeData;

    // LLM settings storage
    struct LLMSettingsData {
        QString lmStudioUrl = "http://localhost:1234";
        QString openaiApiKey = "";
        QString anthropicApiKey = "";
        bool useLocalLLM = true;
    } m_llmSettingsData;

    /**
     * @brief Load settings from persistent storage
     */
    void loadSettings();

    /**
     * @brief Save settings to persistent storage
     */
    void saveSettings();

    /**
     * @brief Test LM Studio connection
     * @param url URL to test
     * @param callback Function to call with result
     */
    void testLMStudioConnection(const QString &url, std::function<void(bool, QString)> callback);

    /**
     * @brief Update LM Studio status display
     */
    void updateLMStudioStatus();
    
private:
    // Core components
    AAFReader *m_aafReader;
    MemoryManager *m_memoryManager;
    PythonBridge *m_pythonBridge;
    LibAAFWrapper *m_libAAFWrapper;
    AudioAnalyzer *m_audioAnalyzer;
    AudioFileManager *m_audioFileManager;

    // Phase 3 components
    ProgressTracker *m_progressTracker;
    AnalysisSettings *m_analysisSettings;
    ClassificationEngine *m_classificationEngine;
    TrackOrganizer *m_trackOrganizer;

    // Phase 4 components
    LMStudioClient *m_lmStudioClient;
    AudioPlaybackManager *m_audioPlaybackManager;
    AAFExporter *m_aafExporter;
    ClassificationReviewDialog *m_reviewDialog;

    // Phase 5 components
    TimelineWidget *m_timelineWidget;
    
    // UI components
    QWidget *m_centralWidget;
    QSplitter *m_mainSplitter;
    QSplitter *m_rightSplitter;
    QTabWidget *m_tabWidget;
    
    // File operations
    QGroupBox *m_fileGroup;
    QPushButton *m_openFileButton;
    QPushButton *m_closeFileButton;
    QLabel *m_fileInfoLabel;
    QLabel *m_timecodeLabel;
    QProgressBar *m_progressBar;
    
    // Classification analysis progress
    QGroupBox *m_analysisProgressGroup;
    QLabel *m_analysisStatusLabel;
    QProgressBar *m_analysisProgressBar;
    
    // Memory management
    QGroupBox *m_memoryGroup;
    QPushButton *m_memoryStatsButton;
    QLabel *m_memoryUsageLabel;
    QProgressBar *m_memoryProgressBar;

    // Audio Analysis
    QGroupBox *m_audioGroup;
    QPushButton *m_analyzeAudioButton;
    QPushButton *m_extractFeaturesButton;
    QPushButton *m_speakerDiarizationButton;
    QLabel *m_audioAnalysisStatus;
    QProgressBar *m_audioProgressBar;

    // Phase 3: Progress Tracking (Top Panel)
    QGroupBox *m_progressGroup;
    QLabel *m_progressTaskLabel;
    QProgressBar *m_mainProgressBar;
    QLabel *m_progressTimeLabel;
    QLabel *m_progressETALabel;
    QPushButton *m_pauseButton;
    QPushButton *m_stopButton;
    QPushButton *m_resumeButton;

    // Phase 3: Analysis Settings Tab
    QWidget *m_settingsTab;
    QGroupBox *m_qualityGroup;
    QComboBox *m_qualityComboBox;
    QGroupBox *m_performanceGroup;
    QSpinBox *m_chunkSkipSpinBox;
    QSpinBox *m_maxConcurrentSpinBox;
    QCheckBox *m_fastModeCheckBox;
    QGroupBox *m_classificationGroup;
    QDoubleSpinBox *m_confidenceSpinBox;
    QCheckBox *m_speakerDiarizationCheckBox;
    QCheckBox *m_musicDetectionCheckBox;
    QCheckBox *m_sfxDetectionCheckBox;
    QPushButton *m_resetSettingsButton;
    QPushButton *m_saveSettingsButton;

    // Time Range Selection for Analysis
    QGroupBox *m_timeRangeGroup;
    QCheckBox *m_analyzeFullAAFCheckBox;
    QLineEdit *m_startTimeEdit;
    QLineEdit *m_endTimeEdit;
    QPushButton *m_setFromTimelineButton;
    QPushButton *m_clearTimeRangeButton;

    // Phase 3: Classification Tab
    QWidget *m_classificationTab;
    QGroupBox *m_classificationControlGroup;
    QPushButton *m_startClassificationButton;
    QPushButton *m_pauseClassificationButton;
    QPushButton *m_stopClassificationButton;
    QPushButton *m_analysisSettingsButton;
    QLabel *m_classificationStatusLabel;
    QProgressBar *m_classificationProgressBar;
    QGroupBox *m_classificationResultsGroup;
    QTableWidget *m_classificationTable;
    QPushButton *m_exportClassificationButton;
    QPushButton *m_reviewClassificationButton;
    QPushButton *m_saveClassificationButton;
    QPushButton *m_loadClassificationButton;

    // Phase 3: Track Organization Tab
    QWidget *m_organizationTab;
    QGroupBox *m_templateGroup;
    QComboBox *m_templateComboBox;
    QPushButton *m_managePresetsButton;
    QPushButton *m_previewOrganizationButton;
    QPushButton *m_applyOrganizationButton;
    QCheckBox *m_preserveTimingCheckBox;
    QGroupBox *m_organizationResultsGroup;
    QTabWidget *m_organizationViewTabs;
    QTableWidget *m_organizationTable;
    TimelineWidget *m_organizationTimelineWidget;
    QLabel *m_organizationStatsLabel;



    // LLM Provider Selection UI
    QGroupBox *m_llmProviderGroup;
    QRadioButton *m_localLLMRadio;
    QRadioButton *m_onlineLLMRadio;
    QPushButton *m_llmSettingsButton;

    // Export tab UI elements
    QWidget *m_exportTab;
    QGroupBox *m_exportFormatGroup;
    QRadioButton *m_aafExportRadio;
    QRadioButton *m_omfExportRadio;
    QRadioButton *m_xmlExportRadio;
    QRadioButton *m_csvExportRadio;
    QRadioButton *m_jsonExportRadio;

    QGroupBox *m_exportOptionsGroup;
    QCheckBox *m_includeAudioCheckBox;
    QCheckBox *m_includeMetadataCheckBox;
    QCheckBox *m_includeTimecodeCheckBox;
    QCheckBox *m_includeClassificationCheckBox;
    QCheckBox *m_includeOrganizationCheckBox;

    QGroupBox *m_exportQualityGroup;
    QComboBox *m_audioQualityComboBox;
    QComboBox *m_sampleRateComboBox;
    QComboBox *m_bitDepthComboBox;

    QGroupBox *m_exportDestinationGroup;
    QLineEdit *m_exportPathLineEdit;
    QLineEdit *m_exportFileNameLineEdit;
    QPushButton *m_browseExportPathButton;
    QPushButton *m_startExportButton;

    QProgressBar *m_exportProgressBar;
    QLabel *m_exportStatusLabel;

    // Export Time Range Controls
    QGroupBox *m_exportTimeRangeGroup;
    QRadioButton *m_exportFullAAFRadio;
    QRadioButton *m_exportAnalysisRangeRadio;
    QRadioButton *m_exportCustomRangeRadio;
    QLineEdit *m_exportStartTimeEdit;
    QLineEdit *m_exportEndTimeEdit;

    // Log output
    QGroupBox *m_logGroup;
    QTextEdit *m_logTextEdit;
    QPushButton *m_clearLogButton;

    // Status bar
    QLabel *m_statusLabel;
    QTimer *m_statusTimer;

    // Network manager for LLM connection testing
    QNetworkAccessManager *m_networkManager;
    
    /**
     * @brief Setup user interface
     */
    void setupUI();
    
    /**
     * @brief Setup menu bar
     */
    void setupMenuBar();
    
    /**
     * @brief Setup status bar
     */
    void setupStatusBar();
    
    /**
     * @brief Setup connections between components
     */
    void setupConnections();
    
    /**
     * @brief Create file operations group
     * @return File operations group widget
     */
    QGroupBox* createFileOperationsGroup();
    
    /**
     * @brief Create classification progress group
     * @return Classification progress group widget
     */
    QGroupBox* createClassificationProgressGroup();
    
    /**
     * @brief Create memory management group
     * @return Memory management group widget
     */
    QGroupBox* createMemoryManagementGroup();
    
    /**
     * @brief Create log output group
     * @return Log output group widget
     */
    QGroupBox* createLogOutputGroup();

    /**
     * @brief Create progress tracking group
     * @return Progress tracking group widget
     */
    QGroupBox* createProgressTrackingGroup();

    /**
     * @brief Create analysis settings tab
     * @return Analysis settings tab widget
     */
    QWidget* createAnalysisSettingsTab();

    /**
     * @brief Create classification tab
     * @return Classification tab widget
     */
    QWidget* createClassificationTab();

    /**
     * @brief Create track organization tab
     * @return Track organization tab widget
     */
    QWidget* createTrackOrganizationTab();

    /**
     * @brief Create export tab
     * @return Export tab widget
     */
    QWidget* createExportTab();

    /**
     * @brief Create timeline tab (Phase 5)
     * @return Timeline tab widget
     */
    QWidget* createTimelineTab();


    
    /**
     * @brief Add log message
     * @param message Log message
     * @param isError Whether message is an error
     */
    void addLogMessage(const QString &message, bool isError = false);
    
    /**
     * @brief Update file information display
     */
    void updateFileInfo();
    
    /**
     * @brief Update classification progress display
     * @param progress Progress percentage (0-100, -1 to hide)
     * @param status Status message
     */
    void updateClassificationProgress(int progress, const QString &status);
    
    /**
     * @brief Update memory usage display
     */
    void updateMemoryUsage();
    
    /**
     * @brief Format bytes for display
     * @param bytes Number of bytes
     * @return Formatted string
     */
    QString formatBytes(qint64 bytes);
};

#endif // MAINWINDOW_H
