# WAAFer - AI-Powered AAF Audio Organizer

WAAFer is an AI-powered AAF (Advanced Authoring Format) audio organizer for post-production workflows. It automatically classifies and organizes audio regions in AAF files into structured tracks (Speech, Music, Ambience, SFX) using AI and metadata analysis.

## Features

- **Cross-Platform**: Built for both macOS and Windows
- **AI-Powered Classification**: Uses OpenAI, Claude, Gemini, and local LLMs for audio classification
- **Large File Support**: Handles up to 200GB AAF files with 16MB chunked streaming
- **Memory Efficient**: 16GB RAM target with intelligent memory management
- **Real-time Processing**: Live audio analysis and speaker diarization
- **Modern UI**: Qt6 + QML interface with timeline visualization

## Technology Stack

- **Core Engine**: C++ with Qt6 (MSVC on Windows, Clang on macOS)
- **Audio Processing**: Python integration with librosa, PyAnnote, soundfile
- **AAF Handling**: pyaaf2 for AAF file parsing
- **AI Integration**: OpenAI, Claude, Gemini APIs + llama.cpp for local processing
- **Build System**: CMake + vcpkg/Homebrew

## Quick Start (macOS)

### Prerequisites

1. **Install Homebrew** (if not already installed):
   ```bash
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   ```

2. **Install system dependencies**:
   ```bash
   brew install cmake qt6 python@3.11
   ```

### Setup and Build

1. **Clone and setup the project**:
   ```bash
   git clone <repository-url>
   cd WAAFer
   ```

2. **Create Python virtual environment**:
   ```bash
   python3.11 -m venv waafer_env
   source waafer_env/bin/activate
   ```

3. **Install Python dependencies**:
   ```bash
   # Core packages
   pip install numpy scipy
   
   # Audio processing
   pip install librosa soundfile pyaaf2
   
   # AI/ML packages
   pip install torch --index-url https://download.pytorch.org/whl/cpu
   pip install pyannote.audio
   
   # API clients
   pip install openai anthropic
   ```

4. **Test Python environment**:
   ```bash
   python python_test.py
   ```

5. **Build the application**:
   ```bash
   ./build.sh
   ```

6. **Run WAAFer**:
   ```bash
   cd build
   open WAAFer.app
   ```

## Development Phases

### Phase 1: Core Skeleton
- CMake + vcpkg project structure
- Qt6 + QML GUI shell
- Python C API integration
- Basic AAF reader stub
- Memory manager foundation

### 🔄 Phase 2: Audio Analysis & File Streaming (IN PROGRESS)
- MappedFileReader with 16MB chunk streaming
- Librosa integration for MFCC/spectral features
- WebRTC VAD embedding
- Region structure and feature cache (LRU)
- Basic speaker diarization using pyannote.audio

### 📋 Phase 3: Track Organization Logic
- Classification-to-track mapping logic
- Default templates (Speech, Music, Ambience, SFX)
- Conflict resolution and timeline adjustment
- JSON output of reorganized track layout

### 📋 Phase 4: AI Integration Layer
- OpenAI/Anthropic/Gemini API integration
- llama.cpp local fallback
- Prompt engine (audio features → templates)
- Thresholding + interactive fallback

### 📋 Phase 5: Mini-DAW UI
- Timeline view with ruler and region rendering
- Transport controls (play/pause/stop)
- Region operations (rename, assign, trim)
- Real-time updates from analysis layer

### 📋 Phase 6: Export Engine
- Region re-mapping via pyaaf2
- Preserve original timing
- Output reorganized AAF file

## Project Structure

```
WAAFer/
├── src/
│   ├── main.cpp                 # Application entry point
│   ├── core/                    # Core engine components
│   │   ├── AAFReader.{h,cpp}    # AAF file parsing
│   │   ├── MemoryManager.{h,cpp} # Memory management
│   │   └── ChunkedFileReader.{h,cpp} # File streaming
│   ├── python/                  # Python integration
│   │   └── PythonBridge.{h,cpp} # C++ ↔ Python bridge
│   └── ui/                      # User interface
│       └── MainWindow.{h,cpp}   # Main application window
├── qml/                         # QML interface files
│   ├── main.qml                 # Main QML interface
│   └── qml.qrc                  # QML resources
├── CMakeLists.txt               # Build configuration
├── build.sh                     # Build script
├── python_test.py               # Python environment test
└── README.md                    # This file
```

## Configuration

### Memory Management
- Default: 16GB RAM limit with 16MB chunks
- Configurable via MemoryManager::initialize()
- LRU cache eviction for large files

### Python Environment
- Virtual environment: `waafer_env/`
- Python 3.11 required for optimal compatibility
- All packages installed in isolated environment

### Qt6 Configuration
- macOS: Homebrew installation preferred
- Windows: Qt Online Installer with MSVC
- QML + C++ integration for modern UI

## Testing

### Python Environment Test
```bash
source waafer_env/bin/activate
python python_test.py
```

### Build Test
```bash
./build.sh
```

### Manual Testing
1. Launch WAAFer application
2. Test Python integration via "Tools" menu
3. Load a sample AAF file (if available)
4. Monitor memory usage and log output

## Troubleshooting

### Common Issues

1. **Qt6 not found**:
   ```bash
   brew install qt6
   export Qt6_DIR=/usr/local/lib/cmake/Qt6
   ```

2. **Python packages missing**:
   ```bash
   source waafer_env/bin/activate
   pip install --upgrade pip
   pip install -r requirements.txt  # If available
   ```

3. **CMake configuration fails**:
   - Check Qt6 installation path
   - Verify Python 3.11 is available
   - Ensure virtual environment is activated

4. **Memory mapping issues**:
   - Check file permissions
   - Verify available system memory
   - Try smaller chunk sizes

### Debug Mode

Build in debug mode for detailed logging:
```bash
cd build
cmake -DCMAKE_BUILD_TYPE=Debug ..
make
```

## Contributing

1. Follow the existing code style and structure
2. Test all changes with both real and mock AAF files
3. Ensure cross-platform compatibility (macOS/Windows)
4. Update documentation for new features
5. Add unit tests for core functionality

## License

Copyright © 2025 WAAFer Audio Organizer. All rights reserved.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the log output in the application
3. Test the Python environment with `python_test.py`
4. Verify all dependencies are correctly installed
